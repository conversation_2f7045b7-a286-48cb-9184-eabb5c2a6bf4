import { create } from 'zustand';
import { useOrdersStore } from '../../../stores/ordersStore';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';

export type AnalyticsTimeRange = '7d' | '30d' | '90d' | '1y';

export interface DailyRevenue {
  date: string;
  revenue: number;
  orders: number;
}

export interface ProductPerformance {
  id: string;
  name: string;
  sales: number;
  revenue: number;
  orders: number;
}

export interface AnalyticsData {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  completionRate: number;
  dailyRevenue: DailyRevenue[];
  topProducts: ProductPerformance[];
  peakHours: { hour: number; orders: number }[];
  categoryBreakdown: { category: string; revenue: number; percentage: number }[];
}

interface SupplierAnalyticsStore {
  timeRange: AnalyticsTimeRange;
  setTimeRange: (range: AnalyticsTimeRange) => void;
  getAnalyticsData: (supplierId: string) => AnalyticsData;
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
}

export const useSupplierAnalytics = create<SupplierAnalyticsStore>((set, get) => ({
  timeRange: '30d',
  isLoading: false,
  
  setTimeRange: (range) => set({ timeRange: range }),
  setLoading: (loading) => set({ isLoading: loading }),
  
  getAnalyticsData: (supplierId: string) => {
    const orders = useOrdersStore.getState().orders;
    const supplierOrders = orders.filter(order => order.supplier.id === supplierId);
    
    // Calculate date range
    const now = new Date();
    const { timeRange } = get();
    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));
    
    // Filter orders by date range
    const filteredOrders = supplierOrders.filter(order => {
      const orderDate = new Date(order.createdAt || now);
      return orderDate >= startDate;
    });
    
    // Calculate metrics
    const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.total, 0);
    const totalOrders = filteredOrders.length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const completedOrders = filteredOrders.filter(order => order.status === 'Delivered').length;
    const completionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
    
    // Generate daily revenue data
    const dailyRevenue: DailyRevenue[] = [];
    for (let i = daysBack - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
      const dateStr = date.toISOString().split('T')[0];
      const dayOrders = filteredOrders.filter(order => {
        const orderDate = new Date(order.createdAt || now);
        return orderDate.toISOString().split('T')[0] === dateStr;
      });
      
      dailyRevenue.push({
        date: dateStr,
        revenue: dayOrders.reduce((sum, order) => sum + order.total, 0),
        orders: dayOrders.length
      });
    }
    
    // Calculate top products
    const productSales: { [key: string]: ProductPerformance } = {};
    filteredOrders.forEach(order => {
      order.items.forEach(item => {
        if (!productSales[item.product.id]) {
          productSales[item.product.id] = {
            id: item.product.id.toString(),
            name: item.product.name,
            sales: 0,
            revenue: 0,
            orders: 0
          };
        }
        productSales[item.product.id].sales += item.qty
        productSales[item.product.id].revenue += item.finalPrice * item.qty;
        productSales[item.product.id].orders += 1;
      });
    });
    
    const topProducts = Object.values(productSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
    
    // Calculate peak hours
    const hourlyOrders: { [key: number]: number } = {};
    for (let i = 0; i < 24; i++) {
      hourlyOrders[i] = 0;
    }
    
    filteredOrders.forEach(order => {
      const hour = new Date(order.createdAt || now).getHours();
      hourlyOrders[hour]++;
    });
    
    const peakHours = Object.entries(hourlyOrders)
      .map(([hour, orders]) => ({ hour: parseInt(hour), orders }))
      .sort((a, b) => b.orders - a.orders);
    
    // Calculate category breakdown
    const categoryRevenue: { [key: string]: number } = {};
    filteredOrders.forEach(order => {
      order.items.forEach(item => {
        const category = item.product.category || 'Other';
        categoryRevenue[category] = (categoryRevenue[category] || 0) + (item.finalPrice * item.qty);
      });
    });
    
    const categoryBreakdown = Object.entries(categoryRevenue)
      .map(([category, revenue]) => ({
        category,
        revenue,
        percentage: totalRevenue > 0 ? (revenue / totalRevenue) * 100 : 0
      }))
      .sort((a, b) => b.revenue - a.revenue);
    
    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      completionRate,
      dailyRevenue,
      topProducts,
      peakHours,
      categoryBreakdown
    };
  }
}));
