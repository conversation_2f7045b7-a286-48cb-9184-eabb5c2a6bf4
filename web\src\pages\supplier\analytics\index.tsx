import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  ShoppingCart, 
  CheckCircle2, 
  Trophy, 
  PieChart, 
  Clock,
  ChevronDown,
  Zap
} from 'lucide-react';
import { useCurrentUserData } from '../../../hooks/useCurrentUserData';
import { useSupplierAnalytics, type AnalyticsTimeRange } from './useSupplierAnalytics';
import { suppliersData } from '../../../../../temp-data/suppliersData';

// Glass Card Component with enhanced shimmer effect
const GlassCard: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <motion.div
    whileHover={{ scale: 1.02, y: -5 }}
    whileTap={{ scale: 0.98 }}
    transition={{ type: "spring", stiffness: 300, damping: 20 }}
    className={`relative group bg-white/15 border border-white/30 rounded-3xl p-8 shadow-2xl overflow-hidden transform-gpu ${className}`}
    style={{
      backdropFilter: 'blur(20px)',
      WebkitBackdropFilter: 'blur(20px)',
      position: 'relative',
    }}
  >
    {/* Enhanced Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full animate-[shimmer_3s_infinite] pointer-events-none" />

    {/* Subtle inner glow */}
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />

    {/* Content */}
    <div className="relative z-10">
      {children}
    </div>
  </motion.div>
);

// Simple Background Orb Component - NO BLUR, LOW Z-INDEX
const FloatingOrb: React.FC<{
  size: number;
  color: string;
  delay: number;
  duration: number;
  x: string;
  y: string;
}> = ({ size, color, delay, duration, x, y }) => (
  <motion.div
    className={`absolute rounded-full ${color}`}
    style={{
      width: size,
      height: size,
      left: x,
      top: y,
      opacity: 0.06, // Very low opacity to prevent blur interference
      zIndex: -1, // Behind everything
      pointerEvents: 'none',
    }}
    animate={{
      x: [0, 30, -20, 0],
      y: [0, -20, 30, 0],
      scale: [1, 1.2, 0.8, 1],
    }}
    transition={{
      duration,
      delay,
      repeat: Infinity,
      ease: "easeInOut",
    }}
  />
);

// Simple Particle System Component - Behind everything
const ParticleSystem: React.FC = () => (
  <div className="absolute inset-0 overflow-hidden pointer-events-none" style={{ zIndex: -2 }}>
    {Array.from({ length: 10 }).map((_, i) => (
      <motion.div
        key={i}
        className="absolute w-1 h-1 bg-white rounded-full opacity-10"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          pointerEvents: 'none',
        }}
        animate={{
          y: [0, -100, 0],
          opacity: [0, 0.1, 0],
          scale: [0, 1, 0],
        }}
        transition={{
          duration: 5 + Math.random() * 3,
          delay: Math.random() * 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
    ))}
  </div>
);

// Loading Component
const LoadingComponent: React.FC = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="text-center space-y-6">
      <motion.div
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        className="w-16 h-16 mx-auto"
      >
        <BarChart3 size={64} className="text-indigo-400" />
      </motion.div>
      <div className="space-y-2">
        <h3 className="text-2xl font-bold text-white">Loading Analytics</h3>
        <p className="text-white/70">Preparing your business insights...</p>
      </div>
    </div>
  </div>
);

// Error Component
const ErrorComponent: React.FC = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="text-center space-y-6">
      <div className="w-16 h-16 mx-auto bg-red-500/20 rounded-2xl flex items-center justify-center">
        <Zap size={32} className="text-red-400" />
      </div>
      <div className="space-y-2">
        <h3 className="text-2xl font-bold text-white">No Data Available</h3>
        <p className="text-white/70">Unable to load analytics data. Please check your account settings.</p>
      </div>
    </div>
  </div>
);

const SupplierAnalyticsPage: React.FC = () => {
  const { user } = useCurrentUserData();
  const supplier = suppliersData.find((s) => s.id === user?.supplierId);
  
  const { timeRange, setTimeRange, getAnalyticsData, isLoading, setLoading } = useSupplierAnalytics();
  const [analyticsData, setAnalyticsData] = useState(() => {
    // Safe initialization with fallback
    try {
      return getAnalyticsData(user?.supplierId || '');
    } catch (error) {
      console.error('Error initializing analytics data:', error);
      return {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        completionRate: 0,
        dailyRevenue: [],
        topProducts: [],
        peakHours: [],
        categoryBreakdown: []
      };
    }
  });
  const [selectOpen, setSelectOpen] = useState(false);

  useEffect(() => {
    if (user?.supplierId) {
      setLoading(true);

      // Simulate loading delay for better UX with cleanup
      const timeoutId = setTimeout(() => {
        try {
          setAnalyticsData(getAnalyticsData(user.supplierId || ''));
        } catch (error) {
          console.error('Error loading analytics data:', error);
          // Set default empty data on error
          setAnalyticsData({
            totalRevenue: 0,
            totalOrders: 0,
            averageOrderValue: 0,
            completionRate: 0,
            dailyRevenue: [],
            topProducts: [],
            peakHours: [],
            categoryBreakdown: []
          });
        } finally {
          setLoading(false);
        }
      }, 500);

      // Cleanup function to prevent memory leaks
      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [timeRange, user?.supplierId, getAnalyticsData, setLoading]);

  const formatCurrency = (amount: number) => {
    try {
      if (typeof amount !== 'number' || isNaN(amount)) return '₪0.00';
      return `₪${amount.toFixed(2)}`;
    } catch (error) {
      console.error('Error formatting currency:', error);
      return '₪0.00';
    }
  };

  const formatPercentage = (value: number) => {
    try {
      if (typeof value !== 'number' || isNaN(value)) return '0.0%';
      return `${value.toFixed(1)}%`;
    } catch (error) {
      console.error('Error formatting percentage:', error);
      return '0.0%';
    }
  };

  const timeRangeOptions = [
    { value: '7d', label: 'Last 7 Days' },
    { value: '30d', label: 'Last 30 Days' },
    { value: '90d', label: 'Last 3 Months' },
    { value: '1y', label: 'Last Year' },
  ];

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Same as supplier home */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />
          <ParticleSystem />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <LoadingComponent />
        </div>
      </div>
    );
  }

  // Show error state if no user data
  if (!user?.supplierId) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Same as supplier home */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />
          <ParticleSystem />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <ErrorComponent />
        </div>
      </div>
    );
  }

  return (
    <>
      <style>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>

      <div className="min-h-screen relative overflow-hidden">
        {/* Background - Behind everything */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900">
          {/* Floating Orbs - Behind everything */}
          <FloatingOrb size={450} color="bg-purple-500" delay={0} duration={25} x="5%" y="15%" />
          <FloatingOrb size={380} color="bg-blue-500" delay={2} duration={30} x="75%" y="25%" />
          <FloatingOrb size={320} color="bg-pink-500" delay={4} duration={22} x="15%" y="65%" />
          <FloatingOrb size={300} color="bg-indigo-500" delay={6} duration={28} x="85%" y="75%" />
          <FloatingOrb size={280} color="bg-cyan-500" delay={8} duration={35} x="45%" y="45%" />
          <FloatingOrb size={200} color="bg-emerald-500" delay={10} duration={20} x="60%" y="10%" />

          {/* Particle System */}
          <ParticleSystem />

          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10 pointer-events-none" />
        </div>

        {/* Main Content Container - Normal z-index to not interfere with header */}
        <div className="relative min-h-screen w-full p-8 pb-24" style={{ zIndex: 1 }}>
          <div className="w-full space-y-10">
            {/* Ultra Enhanced Header */}
            <motion.div
              initial={{ opacity: 0, y: -40, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            >
              <GlassCard className="overflow-hidden border-0 shadow-2xl shadow-indigo-500/40">
                <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 p-8 -m-8 mb-0">
                  {/* Decorative Background Elements */}
                  <div className="absolute top-[-50px] right-[-50px] w-32 h-32 rounded-full bg-white/10" />
                  <div className="absolute bottom-[-30px] left-[-30px] w-20 h-20 rounded-full bg-white/8" />

                  <div className="space-y-6">
                    {/* Main Header Content */}
                    <div className="flex items-center gap-4">
                      <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.4, type: 'spring', damping: 15 }}
                        className="bg-white/20 border-2 border-white/30 rounded-2xl p-4"
                      >
                        <BarChart3 size={36} className="text-white" />
                      </motion.div>

                      <div className="flex-1 space-y-2">
                        <motion.h1
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.6, duration: 0.6 }}
                          className="text-white text-4xl font-black"
                        >
                          Business Analytics
                        </motion.h1>

                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.7, duration: 0.6 }}
                          className="space-y-1"
                        >
                          <div className="flex items-center gap-2">
                            <div className="bg-white/20 rounded-lg px-2 py-1">
                              <span className="text-white text-xs font-semibold">LIVE</span>
                            </div>
                          </div>
                          <p className="text-white/90 text-lg font-medium">
                            {supplier?.name || 'Your Business'} Performance Dashboard
                          </p>
                        </motion.div>
                      </div>
                    </div>

                    {/* Enhanced Stats Preview */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8, duration: 0.6 }}
                      className="grid grid-cols-3 gap-4"
                    >
                      <div className="text-center space-y-1">
                        <p className="text-white/80 text-sm font-medium">TODAY'S REVENUE</p>
                        <p className="text-white text-xl font-black">
                          {formatCurrency(analyticsData.dailyRevenue?.length > 0 ? analyticsData.dailyRevenue[analyticsData.dailyRevenue.length - 1]?.revenue || 0 : 0)}
                        </p>
                      </div>

                      <div className="w-px bg-white/30 mx-auto" />

                      <div className="text-center space-y-1">
                        <p className="text-white/80 text-sm font-medium">ORDERS TODAY</p>
                        <p className="text-white text-xl font-black">
                          {analyticsData.dailyRevenue?.length > 0 ? analyticsData.dailyRevenue[analyticsData.dailyRevenue.length - 1]?.orders || 0 : 0}
                        </p>
                      </div>

                      <div className="w-px bg-white/30 mx-auto" />

                      <div className="text-center space-y-1">
                        <p className="text-white/80 text-sm font-medium">SUCCESS RATE</p>
                        <p className="text-white text-xl font-black">
                          {formatPercentage(analyticsData.completionRate)}
                        </p>
                      </div>
                    </motion.div>

                    {/* Enhanced Time Range Selector */}
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.9, duration: 0.5 }}
                      className="space-y-3"
                    >
                      <div className="space-y-1">
                        <h3 className="text-white text-lg font-bold">Analysis Period</h3>
                        <p className="text-white/70 text-sm">Select timeframe for insights</p>
                      </div>

                      <div className="relative">
                        <button
                          onClick={() => setSelectOpen(!selectOpen)}
                          className="w-full bg-white/15 border-2 border-white/30 rounded-2xl py-4 px-6 text-white font-bold text-left flex items-center justify-between hover:bg-white/20 transition-all duration-300 shadow-lg"
                        >
                          <span>{timeRangeOptions.find(opt => opt.value === timeRange)?.label}</span>
                          <ChevronDown size={18} className={`transition-transform duration-300 ${selectOpen ? 'rotate-180' : ''}`} />
                        </button>

                        <AnimatePresence>
                          {selectOpen && (
                            <motion.div
                              initial={{ opacity: 0, y: -10, scale: 0.95 }}
                              animate={{ opacity: 1, y: 0, scale: 1 }}
                              exit={{ opacity: 0, y: -10, scale: 0.95 }}
                              transition={{ duration: 0.2 }}
                              className="absolute top-full left-0 right-0 mt-2 bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden z-50"
                            >
                              {timeRangeOptions.map((option, index) => (
                                <button
                                  key={option.value}
                                  onClick={() => {
                                    setTimeRange(option.value as AnalyticsTimeRange);
                                    setSelectOpen(false);
                                  }}
                                  className={`w-full py-4 px-6 text-left hover:bg-gray-50 transition-colors duration-200 ${
                                    timeRange === option.value ? 'bg-indigo-600 text-white' : 'text-gray-700'
                                  } ${index !== timeRangeOptions.length - 1 ? 'border-b border-gray-100' : ''}`}
                                >
                                  <div className="flex items-center justify-between">
                                    <span className="font-medium">{option.label}</span>
                                    {timeRange === option.value && (
                                      <CheckCircle2 size={18} className="text-white" />
                                    )}
                                  </div>
                                </button>
                              ))}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </motion.div>
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Enhanced Key Metrics Cards */}
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                className="flex items-center gap-3"
              >
                <div className="bg-gray-100/20 rounded-xl p-2">
                  <TrendingUp size={24} className="text-indigo-400" />
                </div>
                <h2 className="text-white text-3xl font-black">Key Performance Metrics</h2>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Total Revenue */}
                <motion.div
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ delay: 0.3, type: 'spring', damping: 20 }}
                >
                  <GlassCard className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 border-green-400/30 shadow-green-500/20">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="bg-green-500 rounded-xl p-3">
                          <DollarSign size={24} className="text-white" />
                        </div>
                        <div className="bg-green-500/20 rounded-lg px-3 py-1">
                          <span className="text-green-400 text-sm font-bold">+12.5%</span>
                        </div>
                      </div>

                      <div className="space-y-1">
                        <p className="text-green-100 text-lg font-semibold opacity-80">Total Revenue</p>
                        <h3 className="text-green-50 text-3xl font-black">
                          {formatCurrency(analyticsData.totalRevenue)}
                        </h3>
                      </div>
                    </div>
                  </GlassCard>
                </motion.div>

                {/* Total Orders */}
                <motion.div
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ delay: 0.4, type: 'spring', damping: 20 }}
                >
                  <GlassCard className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border-blue-400/30 shadow-blue-500/20">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="bg-blue-500 rounded-xl p-3">
                          <ShoppingCart size={24} className="text-white" />
                        </div>
                        <div className="bg-blue-500/20 rounded-lg px-3 py-1">
                          <span className="text-blue-400 text-sm font-bold">+8.2%</span>
                        </div>
                      </div>

                      <div className="space-y-1">
                        <p className="text-blue-100 text-lg font-semibold opacity-80">Total Orders</p>
                        <h3 className="text-blue-50 text-3xl font-black">
                          {analyticsData.totalOrders}
                        </h3>
                      </div>
                    </div>
                  </GlassCard>
                </motion.div>

                {/* Average Order Value */}
                <motion.div
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ delay: 0.5, type: 'spring', damping: 20 }}
                >
                  <GlassCard className="bg-gradient-to-br from-amber-500/20 to-orange-500/20 border-amber-400/30 shadow-amber-500/20">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="bg-amber-500 rounded-xl p-3">
                          <TrendingUp size={24} className="text-white" />
                        </div>
                        <div className="bg-amber-500/20 rounded-lg px-3 py-1">
                          <span className="text-amber-400 text-sm font-bold">+5.7%</span>
                        </div>
                      </div>

                      <div className="space-y-1">
                        <p className="text-amber-100 text-lg font-semibold opacity-80">Avg Order Value</p>
                        <h3 className="text-amber-50 text-3xl font-black">
                          {formatCurrency(analyticsData.averageOrderValue)}
                        </h3>
                      </div>
                    </div>
                  </GlassCard>
                </motion.div>

                {/* Completion Rate */}
                <motion.div
                  initial={{ opacity: 0, y: 30, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ delay: 0.6, type: 'spring', damping: 20 }}
                >
                  <GlassCard className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 border-purple-400/30 shadow-purple-500/20">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="bg-purple-500 rounded-xl p-3">
                          <CheckCircle2 size={24} className="text-white" />
                        </div>
                        <div className="bg-purple-500/20 rounded-lg px-3 py-1">
                          <span className="text-purple-400 text-sm font-bold">+2.1%</span>
                        </div>
                      </div>

                      <div className="space-y-1">
                        <p className="text-purple-100 text-lg font-semibold opacity-80">Success Rate</p>
                        <h3 className="text-purple-50 text-3xl font-black">
                          {formatPercentage(analyticsData.completionRate)}
                        </h3>
                      </div>
                    </div>
                  </GlassCard>
                </motion.div>
              </div>
            </div>

            {/* Top Products Section */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <GlassCard>
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="bg-gray-100/20 rounded-xl p-2">
                      <Trophy size={24} className="text-yellow-400" />
                    </div>
                    <h3 className="text-white text-2xl font-bold">Top Performing Products</h3>
                  </div>

                  <div className="space-y-4">
                    {analyticsData.topProducts?.length > 0 ? (
                      analyticsData.topProducts.map((product, index) => (
                        <motion.div
                          key={product.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * index }}
                          className="bg-white/10 border border-white/20 rounded-2xl p-4"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4 flex-1">
                              <div
                                className="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm"
                                style={{
                                  backgroundColor: index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32'
                                }}
                              >
                                {index + 1}
                              </div>
                              <div className="flex-1">
                                <h4 className="text-white text-lg font-semibold">{product.name}</h4>
                                <p className="text-white/70">
                                  {product.sales} sold • {product.orders} orders
                                </p>
                              </div>
                            </div>
                            <div className="text-green-400 text-lg font-bold">
                              {formatCurrency(product.revenue)}
                            </div>
                          </div>
                        </motion.div>
                      ))
                    ) : (
                      <div className="bg-white/10 rounded-2xl p-6 text-center">
                        <p className="text-white/70">No sales data available for this period</p>
                      </div>
                    )}
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Category Breakdown */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <GlassCard>
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="bg-gray-100/20 rounded-xl p-2">
                      <PieChart size={24} className="text-pink-400" />
                    </div>
                    <h3 className="text-white text-2xl font-bold">Revenue by Category</h3>
                  </div>

                  <div className="space-y-4">
                    {analyticsData.categoryBreakdown?.length > 0 ? (
                      analyticsData.categoryBreakdown.map((category, index) => (
                        <div key={category.category} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-white text-lg font-semibold">{category.category}</span>
                            <div className="flex items-center gap-3">
                              <span className="text-white/70">{formatPercentage(category.percentage)}</span>
                              <span className="text-green-400 font-semibold">
                                {formatCurrency(category.revenue)}
                              </span>
                            </div>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-2">
                            <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: `${category.percentage}%` }}
                              transition={{ delay: 0.2 * index, duration: 0.8 }}
                              className="h-2 rounded-full"
                              style={{
                                background: `hsl(${(index * 60) % 360}, 70%, 50%)`
                              }}
                            />
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="bg-white/10 rounded-2xl p-6 text-center">
                        <p className="text-white/70">No category data available</p>
                      </div>
                    )}
                  </div>
                </div>
              </GlassCard>
            </motion.div>

            {/* Peak Hours Analysis */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              <GlassCard>
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="bg-gray-100/20 rounded-xl p-2">
                      <Clock size={24} className="text-blue-400" />
                    </div>
                    <h3 className="text-white text-2xl font-bold">Peak Hours</h3>
                  </div>

                  <div className="space-y-4">
                    {analyticsData.peakHours?.slice(0, 5).map((hour) => {
                      const orderCounts = analyticsData.peakHours?.map(h => h.orders) || [];
                      const maxOrders = orderCounts.length > 0 ? Math.max(...orderCounts) : 1;
                      const percentage = maxOrders > 0 ? (hour.orders / maxOrders) * 100 : 0;

                      return (
                        <div key={hour.hour} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-white text-lg font-semibold">
                              {hour.hour}:00 - {hour.hour + 1}:00
                            </span>
                            <span className="text-blue-400 font-semibold">
                              {hour.orders} orders
                            </span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-2">
                            <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: `${percentage}%` }}
                              transition={{ delay: 0.1 * hour.hour, duration: 0.8 }}
                              className="bg-blue-500 h-2 rounded-full"
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </GlassCard>
            </motion.div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SupplierAnalyticsPage;
